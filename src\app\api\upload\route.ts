import { NextRequest, NextResponse } from "next/server";
import { uploadImage, saveProcessedImage } from "@/lib/supabase";
import { ApiResponse, UploadResponse } from "@/types";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const url = formData.get("url") as string;

    if (!file && !url) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "No file or URL provided",
      }, { status: 400 });
    }

    let uploadFile: File;
    let fileName: string;

    if (file) {
      // Validate file
      if (!file.type.startsWith("image/")) {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "File must be an image",
        }, { status: 400 });
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "File size must be less than 10MB",
        }, { status: 400 });
      }

      uploadFile = file;
      fileName = file.name;
    } else {
      // Download from URL
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Failed to fetch image from URL");
        }

        const contentType = response.headers.get("content-type");
        if (!contentType?.startsWith("image/")) {
          return NextResponse.json<ApiResponse>({
            success: false,
            error: "URL does not point to an image",
          }, { status: 400 });
        }

        const blob = await response.blob();
        if (blob.size > 10 * 1024 * 1024) {
          return NextResponse.json<ApiResponse>({
            success: false,
            error: "Image size must be less than 10MB",
          }, { status: 400 });
        }

        uploadFile = new File([blob], "image-from-url", { type: blob.type });
        fileName = "image-from-url";
      } catch (error) {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "Failed to download image from URL",
        }, { status: 400 });
      }
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = uploadFile.name.split(".").pop() || "jpg";
    const uniqueFileName = `original/${timestamp}-${Math.random().toString(36).substring(7)}.${extension}`;

    // Upload to Supabase Storage
    const uploadResult = await uploadImage(uploadFile, uniqueFileName);
    
    // Get public URL
    const { supabase } = await import("@/lib/supabase");
    const { data: { publicUrl } } = supabase.storage
      .from("images")
      .getPublicUrl(uniqueFileName);

    // Save to database
    const imageRecord = await saveProcessedImage({
      original_url: publicUrl,
      status: "uploaded",
    });

    const response: UploadResponse = {
      imageId: imageRecord.id,
      url: uploadResult.path,
      publicUrl: publicUrl,
    };

    return NextResponse.json<ApiResponse<UploadResponse>>({
      success: true,
      data: response,
      message: "Image uploaded successfully",
    });

  } catch (error) {
    console.error("Upload error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
