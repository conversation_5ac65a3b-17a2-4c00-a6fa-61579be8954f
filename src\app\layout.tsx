import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Toaster } from "@/components/ui/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Background Generator - Tạo Background <PERSON>yên <PERSON>p Cho <PERSON>ả<PERSON>",
  description: "Tự động tạo background chuyên nghiệp cho hình ảnh sản phẩm sử dụng AI. N<PERSON><PERSON> chóng, chất lư<PERSON>ng cao, dễ sử dụng.",
  keywords: ["background generator", "AI", "product photography", "image processing", "remove background"],
  authors: [{ name: "Background Generator Team" }],
  openGraph: {
    title: "Background Generator - Tạo Background Chuyê<PERSON>",
    description: "Tự động tạo background chuyên nghiệ<PERSON> cho hình ảnh sản phẩm sử dụng AI",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="relative flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
        <Toaster />
      </body>
    </html>
  );
}
