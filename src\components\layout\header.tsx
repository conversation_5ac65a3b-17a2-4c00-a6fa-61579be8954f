"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const navigation = [
  { name: "Trang Chủ", href: "/" },
  { name: "Upload", href: "/upload" },
  { name: "Gallery", href: "/gallery" },
  { name: "<PERSON><PERSON>", href: "/docs" },
  { name: "About", href: "/about" },
  { name: "Contact", href: "/contact" },
];

export function Header() {
  const pathname = usePathname();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <div className="mr-4 hidden md:flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-purple-600" />
            <span className="hidden font-bold sm:inline-block">
              Background Generator
            </span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            {navigation.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "transition-colors hover:text-foreground/80",
                  pathname === item.href
                    ? "text-foreground"
                    : "text-foreground/60"
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
        
        {/* Mobile menu button */}
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Button asChild className="md:hidden">
              <Link href="/upload">
                Bắt Đầu
              </Link>
            </Button>
          </div>
          <nav className="flex items-center">
            <Button asChild className="hidden md:inline-flex">
              <Link href="/upload">
                Bắt Đầu Ngay
              </Link>
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}
